import { Request, Response, NextFunction } from 'express';
import { verifyFirebaseToken } from '../config/firebase';
import { User, IUser } from '../models/User';

/**
 * Extended Request interface to include user information
 */
export interface AuthenticatedRequest extends Request {
  user?: IUser;
  firebaseUid?: string;
}

/**
 * Authentication middleware for protecting API routes
 * Verifies Firebase ID token and attaches user to request
 */
export const authMiddleware = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    // Extract token from Authorization header
    const authHeader = req.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      res.status(401).json({ 
        error: 'Unauthorized', 
        message: 'No valid authorization token provided' 
      });
      return;
    }

    const idToken = authHeader.split('Bearer ')[1];

    if (!idToken) {
      res.status(401).json({ 
        error: 'Unauthorized', 
        message: 'No token provided' 
      });
      return;
    }

    // Verify Firebase ID token
    const decodedToken = await verifyFirebaseToken(idToken);
    
    if (!decodedToken) {
      res.status(401).json({ 
        error: 'Unauthorized', 
        message: 'Invalid token' 
      });
      return;
    }

    // Find user in database
    const user = await User.findOne({ firebaseUid: decodedToken.uid });
    
    if (!user) {
      res.status(401).json({ 
        error: 'Unauthorized', 
        message: 'User not found' 
      });
      return;
    }

    // Update user's last seen timestamp
    await user.updateLastSeen();

    // Attach user and Firebase UID to request
    req.user = user;
    req.firebaseUid = decodedToken.uid;

    next();
  } catch (error) {
    console.error('Authentication error:', error);
    
    // Handle specific Firebase errors
    if (error instanceof Error) {
      if (error.message.includes('expired')) {
        res.status(401).json({ 
          error: 'Unauthorized', 
          message: 'Token has expired' 
        });
        return;
      }
      
      if (error.message.includes('invalid')) {
        res.status(401).json({ 
          error: 'Unauthorized', 
          message: 'Invalid token format' 
        });
        return;
      }
    }

    res.status(401).json({ 
      error: 'Unauthorized', 
      message: 'Authentication failed' 
    });
  }
};

/**
 * Optional authentication middleware
 * Attaches user to request if token is valid, but doesn't block if invalid
 */
export const optionalAuthMiddleware = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const authHeader = req.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return next();
    }

    const idToken = authHeader.split('Bearer ')[1];
    
    if (!idToken) {
      return next();
    }

    const decodedToken = await verifyFirebaseToken(idToken);
    const user = await User.findOne({ firebaseUid: decodedToken.uid });
    
    if (user) {
      req.user = user;
      req.firebaseUid = decodedToken.uid;
      await user.updateLastSeen();
    }
    
    next();
  } catch (error) {
    // Silently continue without authentication
    next();
  }
};
