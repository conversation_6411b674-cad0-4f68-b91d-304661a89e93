import { Response } from 'express';
import mongoose from 'mongoose';
import { Chat, ChatType } from '../models/Chat';
import { User } from '../models/User';
import { AuthenticatedRequest } from '../middlewares/auth';
import { ApiError } from '../middlewares/errorHandler';

/**
 * Chat controller
 * Handles chat-related operations
 */
export const chatController = {
  /**
   * Get user's chats
   */
  getUserChats: async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    if (!req.user) {
      throw new ApiError(401, 'User not authenticated');
    }

    const { page = 1, limit = 20 } = req.query;
    const pageNum = parseInt(page as string);
    const limitNum = parseInt(limit as string);
    const skip = (pageNum - 1) * limitNum;

    const chats = await Chat.find({
      members: req.user._id,
      isActive: true
    })
    .populate('members', 'name email avatar isOnline lastSeen')
    .populate('lastMessage')
    .sort({ lastActivity: -1 })
    .limit(limitNum)
    .skip(skip);

    const total = await Chat.countDocuments({
      members: req.user._id,
      isActive: true,
    });

    res.status(200).json({
      chats,
      pagination: {
        page: pageNum,
        limit: limitNum,
        total,
        pages: Math.ceil(total / limitNum),
      },
    });
  },

  /**
   * Create new chat (direct or group)
   */
  createChat: async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    if (!req.user) {
      throw new ApiError(401, 'User not authenticated');
    }

    const { type, members, name, description } = req.body;

    // Validate input
    if (!type || !Object.values(ChatType).includes(type)) {
      throw new ApiError(400, 'Valid chat type is required');
    }

    if (!members || !Array.isArray(members) || members.length === 0) {
      throw new ApiError(400, 'Members array is required');
    }

    // Validate member IDs
    const memberIds = members.map(id => {
      if (!mongoose.Types.ObjectId.isValid(id)) {
        throw new ApiError(400, `Invalid member ID: ${id}`);
      }
      return new mongoose.Types.ObjectId(id);
    });

    // For direct chats, ensure exactly 2 members
    if (type === ChatType.DIRECT) {
      if (memberIds.length !== 1) {
        throw new ApiError(400, 'Direct chats must have exactly one other member');
      }

      // Add current user to members
      memberIds.push(req.user._id);

      // Check if direct chat already exists
      const existingChat = await Chat.findOne({
        type: ChatType.DIRECT,
        members: { $all: [req.user._id, memberIds[0]] },
        isActive: true
      });
      if (existingChat) {
        res.status(200).json({ chat: existingChat });
        return;
      }
    }

    // For group chats, validate name
    if (type === ChatType.GROUP && (!name || name.trim().length === 0)) {
      throw new ApiError(400, 'Group chats must have a name');
    }

    // Verify all members exist
    const existingUsers = await User.find({ _id: { $in: memberIds } });
    if (existingUsers.length !== memberIds.length) {
      throw new ApiError(400, 'One or more members not found');
    }

    // Create chat
    const chat = new Chat({
      type,
      name: type === ChatType.GROUP ? name?.trim() : undefined,
      description: description?.trim() || undefined,
      members: memberIds,
      createdBy: req.user._id,
    });

    await chat.save();
    await chat.populate('members', 'name email avatar isOnline lastSeen');

    res.status(201).json({
      message: 'Chat created successfully',
      chat,
    });
  },

  /**
   * Get chat by ID
   */
  getChatById: async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    if (!req.user) {
      throw new ApiError(401, 'User not authenticated');
    }

    const { chatId } = req.params;

    if (!mongoose.Types.ObjectId.isValid(chatId)) {
      throw new ApiError(400, 'Invalid chat ID');
    }

    const chat = await Chat.findOne({
      _id: chatId,
      members: req.user._id,
      isActive: true,
    })
    .populate('members', 'name email avatar isOnline lastSeen')
    .populate('lastMessage');

    if (!chat) {
      throw new ApiError(404, 'Chat not found');
    }

    res.status(200).json({ chat });
  },

  /**
   * Update chat (name, description, etc.)
   */
  updateChat: async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    if (!req.user) {
      throw new ApiError(401, 'User not authenticated');
    }

    const { chatId } = req.params;
    const { name, description, avatar } = req.body;

    if (!mongoose.Types.ObjectId.isValid(chatId)) {
      throw new ApiError(400, 'Invalid chat ID');
    }

    const chat = await Chat.findOne({
      _id: chatId,
      members: req.user._id,
      isActive: true,
    });

    if (!chat) {
      throw new ApiError(404, 'Chat not found');
    }

    // Only group chats can be updated
    if (chat.type !== ChatType.GROUP) {
      throw new ApiError(400, 'Only group chats can be updated');
    }

    // Check if user is admin
    if (!chat.admins.includes(req.user._id)) {
      throw new ApiError(403, 'Only admins can update group chats');
    }

    // Update fields
    if (name !== undefined) {
      if (!name || name.trim().length === 0) {
        throw new ApiError(400, 'Group name cannot be empty');
      }
      chat.name = name.trim();
    }

    if (description !== undefined) {
      chat.description = description?.trim() || undefined;
    }

    if (avatar !== undefined) {
      chat.avatar = avatar?.trim() || undefined;
    }

    await chat.save();
    await chat.populate('members', 'name email avatar isOnline lastSeen');

    res.status(200).json({
      message: 'Chat updated successfully',
      chat,
    });
  },

  /**
   * Delete/leave chat
   */
  deleteChat: async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    if (!req.user) {
      throw new ApiError(401, 'User not authenticated');
    }

    const { chatId } = req.params;

    if (!mongoose.Types.ObjectId.isValid(chatId)) {
      throw new ApiError(400, 'Invalid chat ID');
    }

    const chat = await Chat.findOne({
      _id: chatId,
      members: req.user._id,
      isActive: true,
    });

    if (!chat) {
      throw new ApiError(404, 'Chat not found');
    }

    if (chat.type === ChatType.DIRECT) {
      // For direct chats, just mark as inactive
      chat.isActive = false;
      await chat.save();
    } else {
      // For group chats, remove user from members
      chat.members = chat.members.filter(member => !member.equals(req.user!._id));
      chat.admins = chat.admins.filter(admin => !admin.equals(req.user!._id));
      chat.lastActivity = new Date();

      // If no members left, mark chat as inactive
      if (chat.members.length === 0) {
        chat.isActive = false;
      }
      await chat.save();
    }

    res.status(200).json({
      message: 'Successfully left chat',
    });
  },

  /**
   * Add member to group chat
   */
  addMember: async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    if (!req.user) {
      throw new ApiError(401, 'User not authenticated');
    }

    const { chatId } = req.params;
    const { userId } = req.body;

    if (!mongoose.Types.ObjectId.isValid(chatId)) {
      throw new ApiError(400, 'Invalid chat ID');
    }

    if (!mongoose.Types.ObjectId.isValid(userId)) {
      throw new ApiError(400, 'Invalid user ID');
    }

    const chat = await Chat.findOne({
      _id: chatId,
      members: req.user._id,
      isActive: true,
    });

    if (!chat) {
      throw new ApiError(404, 'Chat not found');
    }

    if (chat.type !== ChatType.GROUP) {
      throw new ApiError(400, 'Can only add members to group chats');
    }

    // Check if user is admin
    if (!chat.admins.includes(req.user._id)) {
      throw new ApiError(403, 'Only admins can add members');
    }

    // Check if user exists
    const userToAdd = await User.findById(userId);
    if (!userToAdd) {
      throw new ApiError(404, 'User not found');
    }

    // Check if user is already a member
    if (chat.members.includes(new mongoose.Types.ObjectId(userId))) {
      throw new ApiError(400, 'User is already a member');
    }

    // Add member to chat
    const userObjectId = new mongoose.Types.ObjectId(userId);
    if (!chat.members.includes(userObjectId)) {
      chat.members.push(userObjectId);
      chat.lastActivity = new Date();
      await chat.save();
    }
    await chat.populate('members', 'name email avatar isOnline lastSeen');

    res.status(200).json({
      message: 'Member added successfully',
      chat,
    });
  },

  /**
   * Remove member from group chat
   */
  removeMember: async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    if (!req.user) {
      throw new ApiError(401, 'User not authenticated');
    }

    const { chatId, userId } = req.params;

    if (!mongoose.Types.ObjectId.isValid(chatId)) {
      throw new ApiError(400, 'Invalid chat ID');
    }

    if (!mongoose.Types.ObjectId.isValid(userId)) {
      throw new ApiError(400, 'Invalid user ID');
    }

    const chat = await Chat.findOne({
      _id: chatId,
      members: req.user._id,
      isActive: true,
    });

    if (!chat) {
      throw new ApiError(404, 'Chat not found');
    }

    if (chat.type !== ChatType.GROUP) {
      throw new ApiError(400, 'Can only remove members from group chats');
    }

    // Check if user is admin or removing themselves
    const isAdmin = chat.admins.includes(req.user._id);
    const isRemovingSelf = userId === req.user._id.toString();

    if (!isAdmin && !isRemovingSelf) {
      throw new ApiError(403, 'Only admins can remove other members');
    }

    // Check if user is a member
    const userObjectId = new mongoose.Types.ObjectId(userId);
    if (!chat.members.includes(userObjectId)) {
      throw new ApiError(400, 'User is not a member');
    }

    // Remove member from chat
    chat.members = chat.members.filter(member => !member.equals(userObjectId));
    chat.admins = chat.admins.filter(admin => !admin.equals(userObjectId));
    chat.lastActivity = new Date();
    await chat.save();
    await chat.populate('members', 'name email avatar isOnline lastSeen');

    res.status(200).json({
      message: 'Member removed successfully',
      chat,
    });
  },
};
