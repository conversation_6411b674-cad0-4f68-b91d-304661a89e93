import mongoose, { Document, Schema, Model } from 'mongoose';

/**
 * User interface for TypeScript
 */
export interface IUser extends Document {
  _id: mongoose.Types.ObjectId;
  firebaseUid: string;
  email: string;
  name: string;
  avatar?: string;
  isOnline: boolean;
  lastSeen: Date;
  createdAt: Date;
  updatedAt: Date;
  setOnline(isOnline: boolean): Promise<IUser>;
  updateLastSeen(): Promise<IUser>;
}

/**
 * User model interface with static methods
 */
export interface IUserModel extends Model<IUser> {
  findByFirebaseUid(firebaseUid: string): Promise<IUser | null>;
  findByEmail(email: string): Promise<IUser | null>;
  getOnlineUsers(): Promise<IUser[]>;
}

/**
 * User schema for MongoDB
 * Stores user information synced with Firebase Auth
 */
const userSchema = new Schema<IUser>({
  firebaseUid: {
    type: String,
    required: true,
    unique: true,
    index: true,
    trim: true,
  },
  email: {
    type: String,
    required: true,
    unique: true,
    lowercase: true,
    trim: true,
    index: true,
  },
  name: {
    type: String,
    required: true,
    trim: true,
    maxlength: 100,
  },
  avatar: {
    type: String,
    trim: true,
    default: null,
  },
  isOnline: {
    type: Boolean,
    default: false,
    index: true,
  },
  lastSeen: {
    type: Date,
    default: Date.now,
    index: true,
  },
}, {
  timestamps: true,
  toJSON: {
    transform: function(doc, ret) {
      // Remove sensitive fields from JSON output
      delete ret.__v;
      return ret;
    }
  }
});

// Indexes for better query performance
userSchema.index({ email: 1, firebaseUid: 1 });
userSchema.index({ isOnline: 1, lastSeen: -1 });

// Instance methods
userSchema.methods.setOnline = function(isOnline: boolean) {
  this.isOnline = isOnline;
  this.lastSeen = new Date();
  return this.save();
};

userSchema.methods.updateLastSeen = function() {
  this.lastSeen = new Date();
  return this.save();
};

// Static methods
userSchema.statics.findByFirebaseUid = function(firebaseUid: string) {
  return this.findOne({ firebaseUid });
};

userSchema.statics.findByEmail = function(email: string) {
  return this.findOne({ email: email.toLowerCase() });
};

userSchema.statics.getOnlineUsers = function() {
  return this.find({ isOnline: true }).select('_id name avatar email');
};

// Pre-save middleware
userSchema.pre('save', function(next) {
  if (this.isModified('email')) {
    this.email = this.email.toLowerCase();
  }
  next();
});

export const User = mongoose.model<IUser, IUserModel>('User', userSchema);
