import mongoose, { Document, Schema, Model } from 'mongoose';

/**
 * Chat type enumeration
 */
export enum ChatType {
  DIRECT = 'direct',
  GROUP = 'group'
}

/**
 * Chat interface for TypeScript
 */
export interface IChat extends Document {
  _id: mongoose.Types.ObjectId;
  type: ChatType;
  name?: string; // For group chats
  description?: string; // For group chats
  avatar?: string; // For group chats
  members: mongoose.Types.ObjectId[];
  admins: mongoose.Types.ObjectId[]; // For group chats
  createdBy: mongoose.Types.ObjectId;
  lastMessage?: mongoose.Types.ObjectId;
  lastActivity: Date;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
  addMember(userId: mongoose.Types.ObjectId): Promise<IChat>;
  removeMember(userId: mongoose.Types.ObjectId): Promise<IChat>;
  updateLastActivity(): Promise<IChat>;
}

/**
 * Chat model interface with static methods
 */
export interface IChatModel extends Model<IChat> {
  findDirectChat(userId1: mongoose.Types.ObjectId, userId2: mongoose.Types.ObjectId): Promise<IChat | null>;
  findUserChats(userId: mongoose.Types.ObjectId, limit?: number, skip?: number): Promise<IChat[]>;
}

/**
 * Chat schema for MongoDB
 * Supports both direct messages and group chats
 */
const chatSchema = new Schema<IChat>({
  type: {
    type: String,
    enum: Object.values(ChatType),
    required: true,
    default: ChatType.DIRECT,
  },
  name: {
    type: String,
    trim: true,
    maxlength: 100,
    // Required for group chats, optional for direct chats
    validate: {
      validator: function(this: IChat, value: string) {
        if (this.type === ChatType.GROUP) {
          return value && value.trim().length > 0;
        }
        return true;
      },
      message: 'Group chats must have a name'
    }
  },
  description: {
    type: String,
    trim: true,
    maxlength: 500,
  },
  avatar: {
    type: String,
    trim: true,
  },
  members: [{
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  }],
  admins: [{
    type: Schema.Types.ObjectId,
    ref: 'User',
  }],
  createdBy: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  lastMessage: {
    type: Schema.Types.ObjectId,
    ref: 'Message',
  },
  lastActivity: {
    type: Date,
    default: Date.now,
    index: true,
  },
  isActive: {
    type: Boolean,
    default: true,
    index: true,
  },
}, {
  timestamps: true,
  toJSON: {
    transform: function(doc, ret) {
      delete ret.__v;
      return ret;
    }
  }
});

// Indexes for better query performance
chatSchema.index({ members: 1, isActive: 1 });
chatSchema.index({ type: 1, lastActivity: -1 });
chatSchema.index({ 'members': 1, 'lastActivity': -1 });

// Validation: Direct chats must have exactly 2 members
chatSchema.pre('save', function(next) {
  if (this.type === ChatType.DIRECT && this.members.length !== 2) {
    return next(new Error('Direct chats must have exactly 2 members'));
  }
  
  if (this.type === ChatType.GROUP && this.members.length < 2) {
    return next(new Error('Group chats must have at least 2 members'));
  }
  
  // Ensure creator is in members array
  if (!this.members.includes(this.createdBy)) {
    this.members.push(this.createdBy);
  }
  
  // For group chats, ensure creator is an admin
  if (this.type === ChatType.GROUP && !this.admins.includes(this.createdBy)) {
    this.admins.push(this.createdBy);
  }
  
  next();
});

// Static methods
chatSchema.statics.findDirectChat = function(userId1: mongoose.Types.ObjectId, userId2: mongoose.Types.ObjectId) {
  return this.findOne({
    type: ChatType.DIRECT,
    members: { $all: [userId1, userId2] },
    isActive: true
  });
};

chatSchema.statics.findUserChats = function(userId: mongoose.Types.ObjectId, limit = 20, skip = 0) {
  return this.find({
    members: userId,
    isActive: true
  })
  .populate('members', 'name email avatar isOnline lastSeen')
  .populate('lastMessage')
  .sort({ lastActivity: -1 })
  .limit(limit)
  .skip(skip);
};

// Instance methods
chatSchema.methods.addMember = function(userId: mongoose.Types.ObjectId) {
  if (!this.members.includes(userId)) {
    this.members.push(userId);
    this.lastActivity = new Date();
    return this.save();
  }
  return Promise.resolve(this);
};

chatSchema.methods.removeMember = function(userId: mongoose.Types.ObjectId) {
  this.members = this.members.filter(member => !member.equals(userId));
  this.admins = this.admins.filter(admin => !admin.equals(userId));
  this.lastActivity = new Date();
  return this.save();
};

chatSchema.methods.updateLastActivity = function() {
  this.lastActivity = new Date();
  return this.save();
};

export const Chat = mongoose.model<IChat, IChatModel>('Chat', chatSchema);
